"""API端点测试"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
import json

from app.main import app
from app.db.models import StrategyParam, SystemStatus, AccountMetric


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


@pytest.mark.api
class TestSystemAPI:
    """系统API测试"""
    
    def test_health_check(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["service"] == "crypto-trader-backend"
    
    def test_ping(self, client):
        """测试ping端点"""
        response = client.get("/api/v1/ping")
        assert response.status_code == 200
        assert response.json() == {"msg": "pong"}


@pytest.mark.api
class TestStrategyAPI:
    """策略API测试"""
    
    @patch('app.api.v1.strategy.get_db')
    def test_get_strategies(self, mock_get_db, client):
        """测试获取策略列表"""
        # Mock数据库会话
        mock_db = AsyncMock()
        mock_get_db.return_value = mock_db
        
        # Mock策略数据
        mock_strategy = StrategyParam(
            id=1,
            symbol="BTCUSDT",
            params={"fast": 10, "slow": 20},
            enabled=True,
            position_pct=2.0
        )
        mock_db.query.return_value.all.return_value = [mock_strategy]
        
        response = client.get("/api/v1/strategy/list")
        assert response.status_code == 200
        
    def test_create_strategy_validation(self, client):
        """测试策略创建参数验证"""
        # 测试无效参数
        invalid_data = {
            "symbol": "",  # 空符号
            "params": {},  # 空参数
        }
        
        response = client.post("/api/v1/strategy/create", json=invalid_data)
        # 应该返回验证错误
        assert response.status_code in [400, 422]


@pytest.mark.api
class TestAccountAPI:
    """账户API测试"""
    
    @patch('app.api.v1.account.get_db')
    def test_get_account_info(self, mock_get_db, client):
        """测试获取账户信息"""
        mock_db = AsyncMock()
        mock_get_db.return_value = mock_db
        
        # Mock账户指标
        mock_metric = AccountMetric(
            balance=10000.0,
            daily_pnl=100.0,
            total_pnl=500.0,
            equity=10500.0
        )
        mock_db.query.return_value.order_by.return_value.first.return_value = mock_metric
        
        response = client.get("/api/v1/account/info")
        assert response.status_code == 200


@pytest.mark.api
class TestMarketAPI:
    """市场API测试"""
    
    def test_market_status(self, client):
        """测试市场状态端点"""
        response = client.get("/api/v1/market/status")
        # 即使没有实际数据，端点也应该响应
        assert response.status_code in [200, 404, 500]


@pytest.mark.integration
class TestAPIIntegration:
    """API集成测试"""
    
    def test_api_error_handling(self, client):
        """测试API错误处理"""
        # 测试不存在的端点
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        
        # 测试无效的HTTP方法
        response = client.delete("/api/v1/ping")
        assert response.status_code == 405
    
    def test_cors_headers(self, client):
        """测试CORS头部"""
        response = client.options("/api/v1/ping")
        # 检查CORS相关头部是否存在
        assert "access-control-allow-origin" in response.headers or response.status_code == 200


@pytest.mark.slow
class TestPerformanceAPI:
    """API性能测试"""
    
    def test_concurrent_requests(self, client):
        """测试并发请求处理"""
        import concurrent.futures
        import time
        
        def make_request():
            return client.get("/api/v1/ping")
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        # 所有请求都应该成功
        assert all(r.status_code == 200 for r in results)
        # 并发处理应该比串行快
        assert end_time - start_time < 5.0  # 20个请求在5秒内完成
