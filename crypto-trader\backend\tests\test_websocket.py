"""WebSocket测试"""
import pytest
import json
import asyncio
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.core.market_ws import MarketBroadcaster
from app.core.log_ws import LogBroadcaster
from app.core.signal_ws import SignalBroadcaster, TradeBroadcaster


@pytest.mark.websocket
class TestWebSocketEndpoints:
    """WebSocket端点测试"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_market_websocket_connection(self, client):
        """测试市场数据WebSocket连接"""
        with client.websocket_connect("/ws/market") as websocket:
            # 连接应该成功建立
            assert websocket is not None
            
            # 可以发送测试消息
            websocket.send_text("ping")
            
            # 连接应该保持活跃
            # 注意：实际的数据推送需要市场数据服务运行
    
    def test_logs_websocket_connection(self, client):
        """测试日志WebSocket连接"""
        with client.websocket_connect("/ws/logs") as websocket:
            assert websocket is not None
    
    def test_signal_websocket_connection(self, client):
        """测试信号WebSocket连接"""
        with client.websocket_connect("/ws/signal") as websocket:
            assert websocket is not None
    
    def test_trade_websocket_connection(self, client):
        """测试交易WebSocket连接"""
        with client.websocket_connect("/ws/trade") as websocket:
            assert websocket is not None


@pytest.mark.websocket
class TestMarketBroadcaster:
    """市场广播器详细测试"""
    
    @pytest.fixture
    def broadcaster(self):
        return MarketBroadcaster()
    
    @pytest.mark.asyncio
    async def test_connection_lifecycle(self, broadcaster):
        """测试连接生命周期"""
        mock_ws = AsyncMock()
        
        # 初始状态
        assert len(broadcaster.connections) == 0
        
        # 添加连接
        await broadcaster.add(mock_ws)
        assert len(broadcaster.connections) == 1
        assert mock_ws in broadcaster.connections
        
        # 移除连接
        await broadcaster.remove(mock_ws)
        assert len(broadcaster.connections) == 0
        assert mock_ws not in broadcaster.connections
    
    @pytest.mark.asyncio
    async def test_multiple_connections(self, broadcaster):
        """测试多个连接管理"""
        connections = [AsyncMock() for _ in range(5)]
        
        # 添加多个连接
        for ws in connections:
            await broadcaster.add(ws)
        
        assert len(broadcaster.connections) == 5
        
        # 广播消息到所有连接
        test_message = {"type": "market", "data": {"symbol": "BTCUSDT", "price": 50000}}
        await broadcaster.broadcast(test_message)
        
        # 所有连接都应该收到消息
        for ws in connections:
            ws.send_text.assert_called_once_with(json.dumps(test_message))
    
    @pytest.mark.asyncio
    async def test_broadcast_error_handling(self, broadcaster):
        """测试广播错误处理"""
        good_ws = AsyncMock()
        bad_ws = AsyncMock()
        bad_ws.send_text.side_effect = Exception("Send failed")
        
        await broadcaster.add(good_ws)
        await broadcaster.add(bad_ws)
        
        test_message = {"type": "test", "data": "message"}
        
        # 广播不应该因为一个连接失败而停止
        await broadcaster.broadcast(test_message)
        
        # 好的连接应该收到消息
        good_ws.send_text.assert_called_once()
        
        # 坏的连接应该被自动移除
        assert bad_ws not in broadcaster.connections
        assert good_ws in broadcaster.connections


@pytest.mark.websocket
class TestLogBroadcaster:
    """日志广播器测试"""
    
    @pytest.fixture
    def log_broadcaster(self):
        return LogBroadcaster()
    
    @pytest.mark.asyncio
    async def test_log_message_broadcast(self, log_broadcaster):
        """测试日志消息广播"""
        mock_ws = AsyncMock()
        await log_broadcaster.add(mock_ws)
        
        log_message = {
            "level": "INFO",
            "message": "Test log message",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        await log_broadcaster.broadcast(log_message)
        mock_ws.send_text.assert_called_once_with(json.dumps(log_message))


@pytest.mark.websocket
class TestSignalBroadcaster:
    """信号广播器测试"""
    
    @pytest.fixture
    def signal_broadcaster(self):
        return SignalBroadcaster()
    
    @pytest.mark.asyncio
    async def test_signal_broadcast(self, signal_broadcaster):
        """测试信号广播"""
        mock_ws = AsyncMock()
        await signal_broadcaster.add(mock_ws)
        
        signal_message = {
            "type": "signal",
            "data": {
                "symbol": "BTCUSDT",
                "signal": "BUY",
                "confidence": 0.85,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
        
        await signal_broadcaster.broadcast(signal_message)
        mock_ws.send_text.assert_called_once_with(json.dumps(signal_message))


@pytest.mark.websocket
class TestTradeBroadcaster:
    """交易广播器测试"""
    
    @pytest.fixture
    def trade_broadcaster(self):
        return TradeBroadcaster()
    
    @pytest.mark.asyncio
    async def test_trade_broadcast(self, trade_broadcaster):
        """测试交易广播"""
        mock_ws = AsyncMock()
        await trade_broadcaster.add(mock_ws)
        
        trade_message = {
            "type": "trade",
            "data": {
                "symbol": "BTCUSDT",
                "side": "BUY",
                "quantity": 0.001,
                "price": 50000,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
        
        await trade_broadcaster.broadcast(trade_message)
        mock_ws.send_text.assert_called_once_with(json.dumps(trade_message))


@pytest.mark.integration
class TestWebSocketIntegration:
    """WebSocket集成测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_websocket_connections(self):
        """测试并发WebSocket连接"""
        broadcaster = MarketBroadcaster()
        
        # 创建多个模拟连接
        connections = [AsyncMock() for _ in range(10)]
        
        # 并发添加连接
        tasks = [broadcaster.add(ws) for ws in connections]
        await asyncio.gather(*tasks)
        
        assert len(broadcaster.connections) == 10
        
        # 并发广播消息
        message = {"type": "test", "data": "concurrent"}
        await broadcaster.broadcast(message)
        
        # 所有连接都应该收到消息
        for ws in connections:
            ws.send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_websocket_memory_cleanup(self):
        """测试WebSocket内存清理"""
        broadcaster = MarketBroadcaster()
        
        # 添加大量连接
        connections = [AsyncMock() for _ in range(100)]
        for ws in connections:
            await broadcaster.add(ws)
        
        assert len(broadcaster.connections) == 100
        
        # 移除所有连接
        for ws in connections:
            await broadcaster.remove(ws)
        
        assert len(broadcaster.connections) == 0
        
        # 确保没有内存泄漏
        import gc
        gc.collect()
        
        # 广播器应该可以重新使用
        new_ws = AsyncMock()
        await broadcaster.add(new_ws)
        assert len(broadcaster.connections) == 1
