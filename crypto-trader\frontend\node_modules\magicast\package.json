{"name": "magicast", "version": "0.3.5", "description": "Modify a JS/TS file and write back magically just like JSON!", "repository": "unjs/magicast", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./helpers": {"import": "./dist/helpers.mjs", "require": "./dist/helpers.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "*.d.ts"], "scripts": {"build": "unbuild", "prepare": "esno ./scripts/vendor.ts", "dev": "vitest dev", "dev:ui": "vitest dev --ui", "lint": "eslint --cache . && prettier -c .", "lint:fix": "eslint --cache . --fix && prettier -c . -w", "prepack": "pnpm run build", "typecheck": "tsc --noEmit", "release": "pnpm run test run && changelogen --release && npm publish && git push --follow-tags", "test": "vitest", "test:build": "TEST_BUILD=true vitest", "test:full": "pnpm run test --run && pnpm run build && pnpm run test:build --run"}, "dependencies": {"@babel/parser": "^7.25.4", "@babel/types": "^7.25.4", "source-map-js": "^1.2.0"}, "devDependencies": {"@types/node": "^20.16.1", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "ast-types": "^0.16.1", "changelogen": "^0.5.5", "eslint": "^9.9.1", "eslint-config-unjs": "^0.3.2", "esno": "^4.7.0", "giget": "^1.2.3", "lint-staged": "^15.2.9", "magicast": "workspace:*", "prettier": "^3.3.3", "recast": "^0.23.9", "simple-git-hooks": "^2.11.1", "source-map": "npm:source-map-js@latest", "typescript": "^5.5.4", "unbuild": "^2.0.0", "vitest": "^1.6.0"}, "resolutions": {"source-map": "npm:source-map-js@latest"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{ts,js,mjs,cjs}": ["eslint --fix", "prettier -w"]}, "packageManager": "pnpm@8.15.9", "pnpm": {"overrides": {"array-includes": "npm:@nolyfill/array-includes@latest", "array.prototype.findlastindex": "npm:@nolyfill/array.prototype.findlastindex@latest", "array.prototype.flat": "npm:@nolyfill/array.prototype.flat@latest", "array.prototype.flatmap": "npm:@nolyfill/array.prototype.flatmap@latest", "hasown": "npm:@nolyfill/hasown@latest", "object.fromentries": "npm:@nolyfill/object.fromentries@latest", "object.groupby": "npm:@nolyfill/object.groupby@latest", "object.values": "npm:@nolyfill/object.values@latest"}}}